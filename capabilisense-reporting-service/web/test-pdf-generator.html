<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CapabiliSense PDF Generator Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        input[type="text"], select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, select:focus {
            border-color: #3498db;
            outline: none;
        }
        .btn {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .api-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
            color: #6c757d;
        }
        .quick-test {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
        .quick-test h3 {
            margin-top: 0;
            color: #856404;
        }
        .quick-test-btn {
            background-color: #ffc107;
            color: #212529;
            padding: 8px 15px;
            border: none;
            border-radius: 3px;
            font-size: 14px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        .quick-test-btn:hover {
            background-color: #e0a800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 CapabiliSense PDF Generator Test</h1>

        <div class="quick-test">
            <h3>Quick Test Options</h3>
            <p>Use these pre-configured project IDs for testing:</p>
            <button class="quick-test-btn" onclick="setProjectId('ai')">AI Project</button>
            <button class="quick-test-btn" onclick="setProjectId('hr')">HR Project</button>
            <button class="quick-test-btn" onclick="setProjectId('mock')">Mock Project</button>
        </div>

        <form id="pdfForm">
            <div class="form-group">
                <label for="projectId">Project ID:</label>
                <input type="text" id="projectId" name="projectId" placeholder="Enter project ID (e.g., 'ai', 'hr')" required>
            </div>

            <div class="form-group">
                <label for="apiUrl">API Base URL:</label>
                <input type="text" id="apiUrl" name="apiUrl" value="http://localhost:8081" placeholder="http://localhost:8081">
            </div>

            <div class="form-group">
                <label for="generationType">Generation Type:</label>
                <select id="generationType" name="generationType">
                    <option value="full">Full Pipeline (Stage A + B + Charts + PDF)</option>
                    <option value="mock">Mock Data (Use existing mock files)</option>
                </select>
            </div>

            <button type="submit" class="btn" id="generateBtn">
                🚀 Generate & Download PDF Report
            </button>
        </form>

        <div id="status" class="status"></div>

        <div class="api-info">
            <h4>📋 How it works:</h4>
            <ul>
                <li><strong>Full Pipeline:</strong> Calls Stage A API → Stage B API → Charts API → Generates PDF</li>
                <li><strong>Mock Data:</strong> Uses existing mock files to generate PDF quickly</li>
                <li><strong>API Endpoints:</strong> Make sure the Combined API is running on port 8081</li>
                <li><strong>Start API:</strong> <code>go run cmd/combined_api/main.go</code></li>
            </ul>
        </div>
    </div>

    <script>
        const form = document.getElementById('pdfForm');
        const status = document.getElementById('status');
        const generateBtn = document.getElementById('generateBtn');

        function setProjectId(id) {
            document.getElementById('projectId').value = id;
        }

        function showStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        function hideStatus() {
            status.style.display = 'none';
        }

        async function generatePDF(projectId, apiUrl, generationType) {
            const baseUrl = apiUrl.replace(/\/$/, ''); // Remove trailing slash

            try {
                if (generationType === 'mock') {
                    // Use mock data approach - call the mock PDF generator directly
                    showStatus('🔄 Generating PDF from mock data...', 'info');

                    // For mock, we'll call a special endpoint that uses the existing mock files
                    const response = await fetch(`${baseUrl}/api/v1/generate-mock-pdf?project_id=${projectId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`Mock PDF generation failed: ${response.status} ${response.statusText}`);
                    }

                    const blob = await response.blob();
                    downloadPDF(blob, `${projectId}_assessment_report_mock.pdf`);
                    showStatus('✅ Mock PDF generated and downloaded successfully!', 'success');

                } else {
                    // Full pipeline approach
                    showStatus('🔄 Step 1/4: Fetching Stage A data...', 'info');

                    // Step 1: Get Stage A data
                    const stageAResponse = await fetch(`${baseUrl}/api/v1/report-data?project_id=${projectId}`);
                    if (!stageAResponse.ok) {
                        throw new Error(`Stage A failed: ${stageAResponse.status} ${stageAResponse.statusText}`);
                    }
                    const stageAResponse_json = await stageAResponse.json();
                    const stageAData = stageAResponse_json.data; // Extract the data field

                    showStatus('🔄 Step 2/4: Generating AI insights...', 'info');

                    // Step 2: Generate Stage B insights
                    const stageBResponse = await fetch(`${baseUrl}/api/v1/generate-insights`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(stageAData)
                    });
                    if (!stageBResponse.ok) {
                        throw new Error(`Stage B failed: ${stageBResponse.status} ${stageBResponse.statusText}`);
                    }
                    const stageBData = await stageBResponse.json();

                    showStatus('🔄 Step 3/4: Generating spider chart...', 'info');

                    // Step 3: Generate chart
                    const chartResponse = await fetch(`${baseUrl}/api/v1/generate-chart`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            stage_a_data: stageAData,
                            chart_type: "spider",
                            config_preset: "professional"
                        })
                    });
                    if (!chartResponse.ok) {
                        throw new Error(`Chart generation failed: ${chartResponse.status} ${chartResponse.statusText}`);
                    }

                    showStatus('🔄 Step 4/4: Generating PDF...', 'info');

                    // Step 4: Generate PDF (this would need a new endpoint)
                    const pdfResponse = await fetch(`${baseUrl}/api/v1/generate-pdf`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            stage_a: stageAData,
                            stage_b: stageBData.data,
                            project_id: projectId
                        })
                    });

                    if (!pdfResponse.ok) {
                        throw new Error(`PDF generation failed: ${pdfResponse.status} ${pdfResponse.statusText}`);
                    }

                    const blob = await pdfResponse.blob();
                    downloadPDF(blob, `${projectId}_assessment_report.pdf`);
                    showStatus('✅ Full pipeline PDF generated and downloaded successfully!', 'success');
                }

            } catch (error) {
                console.error('Error:', error);
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        function downloadPDF(blob, filename) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            hideStatus();

            const projectId = document.getElementById('projectId').value.trim();
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const generationType = document.getElementById('generationType').value;

            if (!projectId) {
                showStatus('❌ Please enter a project ID', 'error');
                return;
            }

            generateBtn.disabled = true;
            generateBtn.textContent = '🔄 Generating...';

            try {
                await generatePDF(projectId, apiUrl, generationType);
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 Generate & Download PDF Report';
            }
        });

        // Check if API is running on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('http://localhost:8081/health');
                if (response.ok) {
                    showStatus('✅ API is running and ready!', 'success');
                } else {
                    showStatus('⚠️ API might not be running. Start with: go run cmd/combined_api/main.go', 'error');
                }
            } catch (error) {
                showStatus('⚠️ API not accessible. Make sure to start: go run cmd/combined_api/main.go', 'error');
            }
        });
    </script>
</body>
</html>
