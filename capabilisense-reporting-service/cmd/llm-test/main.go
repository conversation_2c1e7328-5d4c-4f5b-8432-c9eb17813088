package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"capabilisense-reporting-service/pkg/aiinsights"
	"capabilisense-reporting-service/pkg/config"
)

func main() {
	// Load the prompts library
	library, err := config.LoadPromptsLibrary("configs/prompts_library.json")
	if err != nil {
		log.Fatalf("Failed to load prompts library: %v", err)
	}

	// Create LLM interface
	llmInterface := aiinsights.NewLLMInterface(library)

	// Test basic chat request
	request := aiinsights.LLMRequest{
		PromptID:  "frontend_chat",
		UserQuery: "Hello, can you help me understand the capabilities of this system?",
		RequestID: fmt.Sprintf("test-%d", time.Now().Unix()),
	}

	ctx := context.Background()
	response, err := llmInterface.CallLLM(ctx, request)
	if err != nil {
		log.Fatalf("LLM call failed: %v", err)
	}

	// Print response
	fmt.Printf("Response from %s (%s):\n", response.Provider, response.Model)
	fmt.Printf("Content: %s\n", response.Content)
	fmt.Printf("Tokens used: %d\n", response.TokensUsed)
	fmt.Printf("Processing time: %v\n", response.ProcessingTime)
	fmt.Printf("Used fallback: %t\n", response.UsedFallback)

	// Test with structured output (if schema is configured)
	fmt.Println("\n--- Testing maturity assessor ---")
	request2 := aiinsights.LLMRequest{
		PromptID:  "maturity_assessor",
		UserQuery: "Assess the digital transformation maturity of an organization with strong leadership but weak data governance.",
		RequestID: fmt.Sprintf("test-%d", time.Now().Unix()),
	}

	response2, err := llmInterface.CallLLM(ctx, request2)
	if err != nil {
		log.Printf("Second LLM call failed: %v", err)
	} else {
		fmt.Printf("Response from %s (%s):\n", response2.Provider, response2.Model)
		fmt.Printf("Content: %s\n", response2.Content)
		fmt.Printf("Tokens used: %d\n", response2.TokensUsed)
		fmt.Printf("Processing time: %v\n", response2.ProcessingTime)
		fmt.Printf("Used fallback: %t\n", response2.UsedFallback)
	}

	// Test with chat history
	fmt.Println("\n--- Testing with chat history ---")
	request3 := aiinsights.LLMRequest{
		PromptID: "frontend_chat",
		ChatHistory: []aiinsights.ChatMessage{
			{
				Role:    "user",
				Content: "What is digital transformation?",
			},
			{
				Role:    "assistant",
				Content: "Digital transformation is the process of using digital technologies to create new or modify existing business processes, culture, and customer experiences to meet changing business and market requirements.",
			},
		},
		UserQuery: "Can you give me some specific examples?",
		RequestID: fmt.Sprintf("test-%d", time.Now().Unix()),
	}

	response3, err := llmInterface.CallLLM(ctx, request3)
	if err != nil {
		log.Printf("Third LLM call failed: %v", err)
	} else {
		fmt.Printf("Response from %s (%s):\n", response3.Provider, response3.Model)
		fmt.Printf("Content: %s\n", response3.Content)
		fmt.Printf("Tokens used: %d\n", response3.TokensUsed)
		fmt.Printf("Processing time: %v\n", response3.ProcessingTime)
		fmt.Printf("Used fallback: %t\n", response3.UsedFallback)
	}
}
