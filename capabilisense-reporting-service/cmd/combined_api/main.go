package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"os/signal"
	"syscall"
	"time"

	"capabilisense-reporting-service/pkg/aiinsights"
	"capabilisense-reporting-service/pkg/charts"
	"capabilisense-reporting-service/pkg/config"
	"capabilisense-reporting-service/pkg/dataextraction"
	"capabilisense-reporting-service/pkg/dataextraction/api"
)

func main() {
	// Set up logging
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("Starting CapabiliSense Combined API - Stages A & B")

	// Load environment variables from .env file
	if err := config.LoadTestEnv(); err != nil {
		log.Printf("Warning: Could not load .env file: %v", err)
	}

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	log.Printf("Configuration loaded:")
	log.Printf("  Port: %s", cfg.Port)
	log.Printf("  Database Path: %s", cfg.DatabasePath)
	log.Printf("  Prompts Library: %s", cfg.PromptsLibraryPath)

	// Load prompts library for AI services
	library, err := config.LoadPromptsLibrary(cfg.PromptsLibraryPath)
	if err != nil {
		log.Fatalf("Failed to load prompts library: %v", err)
	}
	log.Println("Prompts library loaded")

	// Initialize database repository for Stage A
	log.Printf("Connecting to database: %s", cfg.DatabasePath)
	repo, err := dataextraction.NewDBRepository(cfg.DatabasePath)
	if err != nil {
		log.Fatalf("Failed to initialize database repository: %v", err)
	}
	defer func() {
		if err := repo.Close(); err != nil {
			log.Printf("Error closing database connection: %v", err)
		}
	}()
	log.Println("Database connection established")

	// Initialize services
	stageAService := dataextraction.NewService(repo)
	log.Println("Stage A service initialized")

	// Create combined HTTP server
	mux := http.NewServeMux()

	// Stage A routes
	stageARoutes := api.SetupRoutes(stageAService)
	mux.Handle("/api/v1/report-data", stageARoutes)
	mux.Handle("/health", stageARoutes)
	mux.Handle("/status", stageARoutes)

	// Stage B routes
	stageBRoutes := aiinsights.SetupStageBRoutes(library)
	mux.Handle("/api/v1/generate-insights", stageBRoutes)
	mux.Handle("/api/v1/combined-report", stageBRoutes)
	mux.Handle("/health-b", stageBRoutes)
	mux.Handle("/stage-b", stageBRoutes)

	// Chart generation routes
	chartOutputDir := "generated_charts"
	baseURL := "http://localhost:" + cfg.Port
	chartRoutes := charts.SetupChartRoutes(chartOutputDir, baseURL)
	mux.Handle("/api/v1/generate-chart", chartRoutes)
	mux.Handle("/api/v1/charts", chartRoutes)
	mux.Handle("/charts/", chartRoutes)

	// PDF generation routes (simplified)
	mux.HandleFunc("/api/v1/generate-mock-pdf", func(w http.ResponseWriter, r *http.Request) {
		// Enable CORS
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Get project ID from query parameter
		projectID := r.URL.Query().Get("project_id")
		if projectID == "" {
			projectID = "ai" // Default to AI project
		}

		// Execute the PDF generator
		cmd := exec.Command("go", "run", "cmd/generate_ai_pdf/main.go")
		cmd.Dir = "." // Current directory

		output, err := cmd.CombinedOutput()
		if err != nil {
			log.Printf("PDF generation failed: %v, output: %s", err, string(output))
			http.Error(w, fmt.Sprintf("PDF generation failed: %v", err), http.StatusInternalServerError)
			return
		}

		// Find the generated PDF file
		pdfPath := fmt.Sprintf("AI_Transformation_Assessment_%s.pdf", time.Now().Format("2006-01-02"))

		// Check if file exists
		if _, err := os.Stat(pdfPath); os.IsNotExist(err) {
			http.Error(w, "PDF file not found", http.StatusNotFound)
			return
		}

		// Read the PDF file
		pdfBytes, err := os.ReadFile(pdfPath)
		if err != nil {
			http.Error(w, "Failed to read PDF file", http.StatusInternalServerError)
			return
		}

		// Set headers for PDF download
		w.Header().Set("Content-Type", "application/pdf")
		w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s_assessment_report.pdf", projectID))
		w.Header().Set("Content-Length", fmt.Sprintf("%d", len(pdfBytes)))

		// Write PDF content
		w.Write(pdfBytes)
	})

	// Serve the test webpage
	mux.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "web/test-pdf-generator.html")
	})

	// Combined service info route
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		response := map[string]interface{}{
			"service":     "CapabiliSense Combined API",
			"version":     "1.0.0",
			"description": "Complete PDF Reporting Pipeline - Data Extraction & AI Insights",
			"stages": map[string]interface{}{
				"stage_a": map[string]interface{}{
					"name":        "Report Data Generation",
					"description": "Extracts and processes assessment data",
					"endpoints": map[string]string{
						"generate_report_data": "/api/v1/report-data?project_id=<project_id>&run_id=<optional_run_id>",
						"health_check":         "/health",
						"status":               "/status",
					},
				},
				"stage_b": map[string]interface{}{
					"name":        "AI Insight Generation",
					"description": "Generates AI-powered insights and recommendations",
					"endpoints": map[string]string{
						"generate_insights": "/api/v1/generate-insights (POST with Stage A output)",
						"health_check":      "/health-b",
						"info":              "/stage-b",
					},
				},
				"charts": map[string]interface{}{
					"name":        "Chart Generation",
					"description": "Generates spider charts and other visualizations",
					"endpoints": map[string]string{
						"generate_chart": "/api/v1/generate-chart (POST with chart data)",
						"list_charts":    "/api/v1/charts",
						"serve_charts":   "/charts/<filename>",
					},
				},
			},
			"pipeline": map[string]interface{}{
				"step_1": "GET /api/v1/report-data?project_id=<id> → Stage A output",
				"step_2": "POST /api/v1/generate-insights with Stage A output → Stage B insights",
				"step_3": "POST /api/v1/generate-chart with Stage A output → Spider chart",
				"step_4": "Combine all outputs for PDF generation",
			},
			"examples": map[string]interface{}{
				"stage_a_call": "curl 'http://localhost:8081/api/v1/report-data?project_id=hr'",
				"stage_b_call": "curl -X POST 'http://localhost:8081/api/v1/generate-insights' -H 'Content-Type: application/json' -d @stage_a_output.json",
				"chart_call":   "curl -X POST 'http://localhost:8081/api/v1/generate-chart' -H 'Content-Type: application/json' -d '{\"stage_a_data\": <stage_a_output>, \"chart_type\": \"spider\"}'",
				"list_charts":  "curl 'http://localhost:8081/api/v1/charts'",
			},
		}

		if err := json.NewEncoder(w).Encode(response); err != nil {
			log.Printf("Failed to encode response: %v", err)
		}
	})

	// Add logging middleware
	handler := api.LoggingMiddleware(mux)

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + cfg.Port,
		Handler:      handler,
		ReadTimeout:  60 * time.Second, // Increased for AI processing
		WriteTimeout: 60 * time.Second, // Increased for AI processing
		IdleTimeout:  120 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting Combined API server on port %s", cfg.Port)
		log.Printf("🚀 CapabiliSense Combined API is running!")
		log.Printf("📊 Stage A (Data): http://localhost:%s/api/v1/report-data?project_id=<id>", cfg.Port)
		log.Printf("🤖 Stage B (AI): http://localhost:%s/api/v1/generate-insights", cfg.Port)
		log.Printf("📈 Charts: http://localhost:%s/api/v1/generate-chart", cfg.Port)
		log.Printf("🔍 Health checks: http://localhost:%s/health (A) & http://localhost:%s/health-b (B)", cfg.Port, cfg.Port)
		log.Printf("📋 API info: http://localhost:%s/", cfg.Port)

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	// Test database connectivity
	testServices(repo, library)

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")
	log.Println("Server stopped")
}

// testServices tests both Stage A and Stage B services
func testServices(repo *dataextraction.DBRepository, library *config.PromptsLibrary) {
	log.Println("🔍 Testing services...")

	// Test Stage A (database connectivity)
	log.Println("✅ Stage A: Database connection successful")
	log.Println("💡 Test Stage A: curl 'http://localhost:8081/api/v1/report-data?project_id=hr'")

	// Test Stage B (prompts library)
	log.Printf("✅ Stage B: Prompts library loaded with %d prompts", len(library.Prompts))
	log.Println("💡 Test Stage B: First get Stage A data, then POST to /api/v1/generate-insights")

	log.Println("🎉 Both services ready!")
}

/*
Complete Pipeline Usage:

1. Get Stage A data:
   curl 'http://localhost:8081/api/v1/report-data?project_id=hr' > stage_a_output.json

2. Generate Stage B insights:
   curl -X POST 'http://localhost:8081/api/v1/generate-insights' \
        -H 'Content-Type: application/json' \
        -d @stage_a_output.json > stage_b_output.json

3. The stage_b_output.json contains:
   - All Stage A data (in report_metadata)
   - AI-generated organization name
   - Executive summary and key findings
   - Domain-specific insights and recommendations
   - AI spotlight analysis
   - ETS solution recommendations
   - Generation metadata (tokens used, providers, etc.)

4. Use both outputs to generate the final PDF report

Example Stage B output structure:
{
  "data": {
    "report_metadata": { ... },
    "organization_name": {
      "generated_name": "HR Excellence Corp",
      "reasoning": "Based on framework focus and assessment data..."
    },
    "overall_summary": {
      "executive_summary": "...",
      "key_findings": [...],
      "strategic_priorities": [...]
    },
    "domain_insights": [...],
    "ai_spotlight": { ... },
    "ets_solutions": { ... },
    "generation_metadata": {
      "generated_at": "...",
      "processing_time": "...",
      "providers_used": [...],
      "total_tokens_used": 1500,
      "fallbacks_used": [...]
    }
  }
}
*/
